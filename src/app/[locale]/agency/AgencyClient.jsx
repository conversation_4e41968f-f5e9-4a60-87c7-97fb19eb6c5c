"use client";

import Hero from '@/components/Heros/Hero';
import StatsCards from '@/components/StatsCards';
import ThreeColumnTitle from '@/components/ThreeColumnTitle';
import Testimonials from '@/components/Testimonials';
import { useTranslation } from "@/hooks/useTranslation";

export default function AgencyClient({ params }) {
  const { t } = useTranslation('pages');
  const locale = params.locale || 'fr';

  return (
    <div>
      <Hero
        title={t('agency.hero_title')}
        subtitle={t('agency.hero_subtitle')}
        locale={locale}
      />
      <StatsCards />
      <ThreeColumnTitle locale={locale} />
      <Testimonials locale={locale} />
    </div>
  );
}
