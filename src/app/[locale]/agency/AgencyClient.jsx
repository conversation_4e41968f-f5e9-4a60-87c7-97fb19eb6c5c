"use client";

import Hero from '@/components/Heros/Hero';
import StatsCards from '@/components/StatsCards';
import ThreeColumnTitle from '@/components/ThreeColumnTitle';
import Description from '@/components/Description';
import Testimonials from '@/components/Testimonials';
import { useTranslation } from "@/hooks/useTranslation";

export default function AgencyClient({ params }) {
  const { t } = useTranslation('pages');
  const locale = params.locale || 'fr';

  return (
    <div>
      <Hero
        title={t('agency.hero_title')}
        subtitle={t('agency.hero_subtitle')}
        locale={locale}
      />
      <StatsCards />
      <ThreeColumnTitle locale={locale} />

      {/* Séparateur */}
      <div className="container">
        <div style={{
          width: '100%',
          height: '1px',
          backgroundColor: '#000',
          margin: '0'
        }} />
      </div>

      <Description
        descriptionTitle={t('agency.values1.title')}
        descriptionText={t('agency.values1.text')}
        showButton={false}
      />

      {/* Séparateur */}
      <div className="container">
        <div style={{
          width: '100%',
          height: '1px',
          backgroundColor: '#000',
          margin: '0'
        }} />
      </div>

      <Description
        descriptionTitle={t('agency.values2.title')}
        descriptionText={t('agency.values2.text')}
        showButton={false}
      />

      {/* Séparateur */}
      <div className="container">
        <div style={{
          width: '100%',
          height: '1px',
          backgroundColor: '#000',
          margin: '0'
        }} />
      </div>

      <Description
        descriptionTitle={t('agency.values3.title')}
        descriptionText={t('agency.values3.text')}
        showButton={false}
      />



      <Testimonials locale={locale} />
    </div>
  );
}
