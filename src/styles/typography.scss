:root {
    font-size: 16px; /* Valeur de base pour rem */
  }

  p, a, li {
    letter-spacing: -0.5px;
    word-spacing: 3px;
  }

  small {
    font-size: 12px;
  }

  li {
    margin: 8px 0;
  }

  h1, h2, h3, h4, h5, h6 {
    margin: 0;
    padding: 0;
  }
  
  
  h1 {
    font-size: calc(1.9rem + 2.8vw);
    font-weight: 500;
    letter-spacing: calc(-1px - 0.2vw); // Adapte la compression des lettres
    word-spacing: calc(5px + 0.5vw);  // Adapte l'espacement entre les mots
    // letter-spacing: -3px;
    // word-spacing: 7px;
  }
  
  h2 {
    font-size: calc(1.8rem + 1.5vw);
    font-weight: 500;
    letter-spacing: calc(-1px - 0.2vw);
    word-spacing: calc(4px + 0.4vw);
    // letter-spacing: -3px;
    // word-spacing: 7px;
  }
  
  h3 {
    font-size: calc(1.2rem + 1.2vw);
    font-weight: 500;
  }
  
  h4 {
    font-size: 1.875rem;
    font-weight: 500;
  }
  
  h5 {
    font-size: 1.5rem;
    font-weight: 500;
  }
  
  h6 {
    font-size: 1.25rem;
    font-weight: 600;
  }
  
  
  .text-big {
    font-size: 1.12rem;
    font-weight: 400;
    line-height: 2rem;
    letter-spacing: -1%;
  }
  
  .text-small, small {
    text-transform: uppercase;
    color: #8899BA;
  }
  
  .text-center {
    display: flex;
    justify-content: center;
    text-align: center;
  }
  
  p {
    line-height: 1.8rem;
  }
  
  @media (min-width: 768px){
    .text-big {
      font-size: 23px;
      font-weight: 400;
      line-height: 2.3rem;
      letter-spacing: -1%;
    }
  }