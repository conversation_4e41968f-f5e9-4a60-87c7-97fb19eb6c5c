import React, { useState, useRef } from "react";
import { motion, useInView } from "framer-motion";
import styles from "./style.module.scss";

// composant parent qui gère l'élément actif
export function Accordion({ children }) {
  const [activeIndex, setActiveIndex] = useState(null);

  function handleClick(index) {
    setActiveIndex((prev) => (prev === index ? null : index));
  }

  return (
    <div className="container">
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return null;
        return React.cloneElement(child, {
          isOpen: activeIndex === index,
          onClick: () => handleClick(index)
        });
      })}
    </div>
  );
}

// Animations pour les éléments de liste
const listItemVariants = {
  hidden: {
    opacity: 0,
    y: 20
  },
  visible: (i) => ({
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      delay: i * 0.1, // <PERSON><PERSON><PERSON> progressif pour effet de cascade
      ease: "easeOut"
    }
  })
};

// Variant pour la flèche avec effet de cache
const arrowVariants = {
  hidden: {
    x: -50,
    opacity: 0
  },
  visible: {
    x: 0,
    opacity: 1,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  },
  active: {
    x: 0,
    opacity: 1,
    rotate: 90,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  }
};

// composant enfant pour un bloc question/réponse enrichi
export function AccordionItem({
  title,
  children,
  isOpen,
  onClick,
  showArrow = false,
  arrowSvg = null,
  items = [],
  ...rest
}) {
  const contentRef = useRef(null);

  return (
    <div className={styles.wrapper}>
      <button
        {...rest}
        onClick={onClick}
        className={`${styles.questionContainer} ${
          isOpen ? styles.questionContainerActive : ""
        }`}
      >
        <div className={styles.headerContent}>
          {/* Flèche personnalisée avec animation */}
          {showArrow && arrowSvg && (
            <div className={styles.arrowContainer}>
              <motion.div
                variants={arrowVariants}
                initial="hidden"
                whileHover="visible"
                animate={isOpen ? "active" : "hidden"}
                className={styles.customArrow}
              >
                {arrowSvg}
              </motion.div>
            </div>
          )}

          <h3 className={styles.questionContent}>{title}</h3>

          {/* Icône par défaut si pas de flèche personnalisée */}
          {!showArrow && (
            <span
              className="material-symbols-outlined"
              style={{
                transform: isOpen ? "rotate(45deg)" : "rotate(0deg)",
                transition: "transform 0.3s ease"
              }}
            >
              add
            </span>
          )}
        </div>
      </button>

      <motion.div
        className={styles.answerContainer}
        ref={contentRef}
        initial={false}
        animate={{
          height: isOpen && contentRef.current ? contentRef.current.scrollHeight : 0,
          opacity: isOpen ? 1 : 0
        }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        <div className={styles.answerContent}>
          {/* Si on a des items, on les anime un par un */}
          {items.length > 0 ? (
            items.map((item, index) => (
              <motion.p
                key={index}
                className="text-big"
                variants={listItemVariants}
                initial="hidden"
                animate={isOpen ? "visible" : "hidden"}
                custom={index}
              >
                {item}
              </motion.p>
            ))
          ) : (
            /* Sinon on affiche le contenu normal */
            children
          )}
        </div>
      </motion.div>
    </div>
  );
}
