import React, { useState, useRef } from "react";
import { motion, useInView } from "framer-motion";
import styles from "./style.module.scss";

// composant parent qui gère l'élément actif
export function Accordion({ children }) {
  const [activeIndex, setActiveIndex] = useState(null);
  const [hoveredIndex, setHoveredIndex] = useState(null);

  function handleClick(index) {
    setActiveIndex((prev) => (prev === index ? null : index));
  }

  return (
    <div className="container">
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return null;
        return React.cloneElement(child, {
          isOpen: activeIndex === index,
          isHovered: hoveredIndex === index,
          isOtherHovered: hoveredIndex !== null && hoveredIndex !== index,
          onClick: () => handleClick(index),
          onMouseEnter: () => setHoveredIndex(index),
          onMouseLeave: () => setHoveredIndex(null)
        });
      })}
    </div>
  );
}

// Animations pour les éléments de liste - Style Awwwards
const listItemVariants = {
  hidden: {
    opacity: 0,
    y: 30,
    scale: 0.95
  },
  visible: (i) => ({
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      delay: i * 0.08, // Délai plus court pour plus de fluidité
      ease: [0.25, 0.46, 0.45, 0.94] // Cubic-bezier premium
    }
  })
};

// Variant pour la flèche avec effet de cache - Style Awwwards
const arrowVariants = {
  hidden: {
    x: -60,
    opacity: 0,
    scale: 0.8
  },
  visible: {
    x: 0,
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94] // Cubic-bezier premium
    }
  },
  active: {
    x: 0,
    opacity: 1,
    scale: 1,
    rotate: 90,
    transition: {
      duration: 0.5,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};

// composant enfant pour un bloc question/réponse enrichi
export function AccordionItem({
  title,
  children,
  isOpen,
  isHovered,
  isOtherHovered,
  onClick,
  onMouseEnter,
  onMouseLeave,
  showArrow = false,
  arrowSvg = null,
  items = [],
  ...rest
}) {
  const contentRef = useRef(null);

  return (
    <div className={styles.wrapper}>
      <button
        {...rest}
        onClick={onClick}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        className={`${styles.questionContainer} ${
          isOpen ? styles.questionContainerActive : ""
        }`}
        style={{
          opacity: isOtherHovered ? 0.3 : 1,
          transition: "all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
        }}
      >
        <div className={styles.headerContent}>
          {/* Flèche personnalisée avec animation */}
          {showArrow && arrowSvg && (
            <div className={styles.arrowContainer}>
              <motion.div
                variants={arrowVariants}
                initial="hidden"
                animate={
                  isOpen ? "active" :
                  isHovered ? "visible" :
                  "hidden"
                }
                className={styles.customArrow}
              >
                {arrowSvg}
              </motion.div>
            </div>
          )}

          <h3 className={styles.questionContent}>{title}</h3>

          {/* Icône par défaut si pas de flèche personnalisée */}
          {!showArrow && (
            <span
              className="material-symbols-outlined"
              style={{
                transform: isOpen ? "rotate(45deg)" : "rotate(0deg)",
                transition: "transform 0.3s ease"
              }}
            >
              add
            </span>
          )}
        </div>
      </button>

      <motion.div
        className={styles.answerContainer}
        ref={contentRef}
        initial={false}
        animate={{
          height: isOpen && contentRef.current ? contentRef.current.scrollHeight : 0,
          opacity: isOpen ? 1 : 0
        }}
        transition={{
          duration: 0.8,
          ease: [0.25, 0.46, 0.45, 0.94] // Cubic-bezier premium
        }}
      >
        <div className={styles.answerContent}>
          {/* Si on a des items, on les anime un par un */}
          {items.length > 0 ? (
            items.map((item, index) => (
              <motion.p
                key={index}
                className="text-big"
                variants={listItemVariants}
                initial="hidden"
                animate={isOpen ? "visible" : "hidden"}
                custom={index}
              >
                {item}
              </motion.p>
            ))
          ) : (
            /* Sinon on affiche le contenu normal */
            children
          )}
        </div>
      </motion.div>
    </div>
  );
}
