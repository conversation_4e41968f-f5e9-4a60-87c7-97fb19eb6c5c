"use client";

import styles from './style.module.scss';
import { useInView, motion } from 'framer-motion';
import { useRef } from 'react';
import { slideUp, opacity } from './animation';
import { Accordion, AccordionItem } from '@/components/AccordionComponent/Accordion';

export default function ExpertiseAccordion({
    title = "Nos expertises",
    expertises = []
}) {
    const container = useRef(null);
    const isInView = useInView(container, { once: true });

    // Protection : s'assurer que expertises est un tableau
    const expertisesList = Array.isArray(expertises) ? expertises : [];

    // SVG de la flèche
    const arrowSvg = (
        <svg
            width="38"
            height="36"
            viewBox="0 0 38 36"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M-5.38965e-07 18H35" stroke="#03134E" strokeWidth="4"/>
            <path d="M19.9999 3.45312L35 17.6199L19.9999 31.7866" stroke="#03134E" strokeWidth="4"/>
        </svg>
    );

    return (
        <div ref={container} className={`${styles.expertiseAccordion} container medium`}>
            <div className={styles.body}>
                {/* Titre à gauche */}
                <div className={styles.titleSection}>
                    <h2>
                        {title.split(" ").map((word, index) => (
                            <span key={index} className={styles.mask}>
                                <motion.span
                                    variants={slideUp}
                                    custom={index}
                                    animate={isInView ? "open" : "closed"}
                                >
                                    {word}
                                </motion.span>
                            </span>
                        ))}
                    </h2>
                </div>

                {/* Accordéon à droite */}
                <motion.div
                    variants={opacity}
                    animate={isInView ? "open" : "closed"}
                    className={styles.accordionSection}
                >
                    <Accordion>
                        {expertisesList.map((expertise, index) => (
                            <AccordionItem
                                key={index}
                                title={expertise.title}
                                showArrow={true}
                                arrowSvg={arrowSvg}
                                items={expertise.items || []}
                            />
                        ))}
                    </Accordion>
                </motion.div>
            </div>
        </div>
    );
}
