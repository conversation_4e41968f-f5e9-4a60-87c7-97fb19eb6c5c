"use client";

import styles from './style.module.scss';
import { useInView, motion } from 'framer-motion';
import { useRef, useState } from 'react';
import { slideUp, opacity } from './animation';

export default function ExpertiseAccordion({ 
    title = "Nos expertises",
    expertises = []
}) {
    const container = useRef(null);
    const isInView = useInView(container, { once: true });
    const [activeIndex, setActiveIndex] = useState(null);

    const toggleAccordion = (index) => {
        setActiveIndex(activeIndex === index ? null : index);
    };

    return (
        <div ref={container} className={`${styles.expertiseAccordion} container medium`}>
            <div className={styles.body}>
                {/* Titre à gauche */}
                <div className={styles.titleSection}>
                    <h2>
                        {title.split(" ").map((word, index) => (
                            <span key={index} className={styles.mask}>
                                <motion.span
                                    variants={slideUp}
                                    custom={index}
                                    animate={isInView ? "open" : "closed"}
                                >
                                    {word}
                                </motion.span>
                            </span>
                        ))}
                    </h2>
                </div>

                {/* Accordéon à droite */}
                <motion.div
                    variants={opacity}
                    animate={isInView ? "open" : "closed"}
                    className={styles.accordionSection}
                >
                    {expertises.map((expertise, index) => (
                        <div key={index} className={styles.accordionItem}>
                            <button
                                className={`${styles.accordionHeader} ${activeIndex === index ? styles.active : ''}`}
                                onClick={() => toggleAccordion(index)}
                            >
                                <div className={styles.headerContent}>
                                    <svg 
                                        className={styles.arrow}
                                        width="38" 
                                        height="36" 
                                        viewBox="0 0 38 36" 
                                        fill="none" 
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path d="M-5.38965e-07 18H35" stroke="#03134E" strokeWidth="4"/>
                                        <path d="M19.9999 3.45312L35 17.6199L19.9999 31.7866" stroke="#03134E" strokeWidth="4"/>
                                    </svg>
                                    <span className={styles.title}>{expertise.title}</span>
                                </div>
                            </button>
                            
                            <motion.div
                                className={styles.accordionContent}
                                initial={false}
                                animate={{
                                    height: activeIndex === index ? "auto" : 0,
                                    opacity: activeIndex === index ? 1 : 0
                                }}
                                transition={{ duration: 0.3, ease: "easeInOut" }}
                            >
                                <div className={styles.contentInner}>
                                    {expertise.items && expertise.items.map((item, itemIndex) => (
                                        <p key={itemIndex} className="text-big">{item}</p>
                                    ))}
                                </div>
                            </motion.div>
                        </div>
                    ))}
                </motion.div>
            </div>
        </div>
    );
}
