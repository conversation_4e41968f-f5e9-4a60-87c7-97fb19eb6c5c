.expertiseAccordion {
    display: flex;
    flex-direction: column;
    padding-top: var(--section-padding);
    padding-bottom: var(--section-padding);
    justify-content: center;
}

.body {
    display: flex;
    flex-direction: column;
    gap: var(--gap-padding);
    width: 100%;
}

.titleSection {
    h2 {
        margin: 0;
    }
}

.mask {
    overflow: hidden;
    display: inline-block;
    margin-right: 0.3em;
}

.accordionSection {
    width: 100%;
}

.accordionItem {
    border-bottom: 1px solid #E5E5E5;
    
    &:first-child {
        border-top: 1px solid #E5E5E5;
    }
}

.accordionHeader {
    width: 100%;
    background: none;
    border: none;
    padding: var(--gap-padding) 0;
    cursor: pointer;
    text-align: left;
    transition: all 0.3s ease;
    
    &:hover {
        .arrow {
            transform: translateX(10px);
        }
    }
    
    &.active {
        .arrow {
            transform: translateX(10px) rotate(90deg);
        }
    }
}

.headerContent {
    display: flex;
    align-items: center;
    gap: var(--gap-padding);
}

.arrow {
    width: 38px;
    height: 36px;
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.title {
    font-size: calc(1.2rem + 1.2vw);
    font-weight: 500;
    color: #03134E;
}

.accordionContent {
    overflow: hidden;
}

.contentInner {
    padding-bottom: var(--gap-padding);
    
    p {
        margin: 0.5rem 0;
        color: #666;
        
        &:first-child {
            margin-top: 0;
        }
        
        &:last-child {
            margin-bottom: 0;
        }
    }
}

// Ajustements pour tablettes et desktops
@media (min-width: 768px) {
    .expertiseAccordion {
        align-items: stretch;
    }

    .body {
        display: grid;
        grid-template-columns: 1fr 1.5fr; // 40% / 60% ratio
        gap: var(--gap-padding);
        max-width: 1400px;
        align-items: start;
        width: 100%;
    }
    
    .accordionHeader {
        padding: calc(var(--gap-padding) * 1.2) 0;
    }
    
    .title {
        font-size: calc(1.4rem + 0.8vw);
    }
}
