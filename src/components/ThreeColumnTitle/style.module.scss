@import "../../styles/variables.scss";

.threeColumnTitle {
  background: #FCFCF8;
}

.grid {
  display: flex;
  flex-direction: column;
  gap: var(--gap-padding);
}

// Colonnes communes
.column {
  display: flex;
  flex-direction: column;
  gap: calc(var(--gap-padding) / 2);
}


.imageWrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.image {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
}

// Tablet - 768px+
@media (min-width: $breakpoint-sm) {
  .grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: var(--gap-padding);
  }

  .column:nth-child(1) {
    grid-column: 1;
    grid-row: 1;
    align-self: end;
  }

  .column:nth-child(2) {
    grid-column: 2;
    grid-row: 1;
  }

  .column:nth-child(3) {
    grid-column: 1 / -1;
    grid-row: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--gap-padding);
    align-items: center;

    .imageWrapper {
      grid-column: 1;
    }

    .description {
      grid-column: 2;
      margin-top: 0;
    }
  }
}

// Desktop - 1200px+
@media (min-width: $breakpoint-lg) {
  .grid {
    display: grid;
    grid-template-columns: 1fr 1.5fr 1fr; // Colonne du milieu plus large
    grid-template-rows: auto;
    gap: var(--gap-padding);
    align-items: start;
  }

  .column:nth-child(1) {
    grid-column: 1;
    grid-row: 1;
    align-self: end;
  }

  .column:nth-child(2) {
    grid-column: 2;
    grid-row: 1;
  }

  .column:nth-child(3) {
    grid-column: 3;
    grid-row: 1;
    display: flex;
    flex-direction: column;
    gap: calc(var(--gap-padding) / 2);

    .description {
      margin-top: calc(var(--gap-padding) / 2);
    }
  }
}
